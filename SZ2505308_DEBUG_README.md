# SZ2505308项目P01屏柜0.3线径调试功能说明

## 功能概述

为了方便调试SZ2505308项目中P01屏柜的0.3线径压头匹配问题，在`terminal_matching.py`中增加了专门的调试输出功能。

## 触发条件

调试输出会在以下条件**同时满足**时自动启用：

1. **项目标识**：数据中包含"SZ2505308"字符串
2. **屏柜编号**：屏柜编号为"P01"
3. **线径规格**：线径为0.3
4. **处理阶段**：在导线统计表的压头匹配阶段

## 调试输出内容

当触发调试模式时，会显示以下详细信息：

### 1. 设备处理开始
```
🎯 [SZ2505308-P01-0.3调试] 开始处理设备: KM1 (起点)
  📦 屏柜编号: P01
  📏 线径: 0.3
  🎨 线材标识: U1 (包含U: True, 包含I: False)
  📊 导线根数: 2
```

### 2. 接口类型匹配结果
```
✅ 找到 1 个匹配的接口类型
  接口1: 线材属性='U', 接口类型='单长', 概率='0.8'
```

### 3. 接口类型处理过程
```
🔄 处理接口类型 1: 单长 (概率: 0.8)
   解析结果: 并线要求=单, 长度类型=长
```

### 4. 压头匹配成功
```
✅ 匹配成功!
  压头名称: 0.3单长压头
  星瀚编码: XH001
  星空编码: XK001
  数量计算: 2 根 × 0.8 概率 = 2
  损耗率: 0.1
```

### 5. 压头匹配失败（如果发生）
```
❌ 匹配失败!
  查找条件: 并线要求=单, 线径=0.3
  接口类型: 长
  失败原因: 未找到符合条件的压头
  Sheet2中的匹配尝试:
    找到2条相关记录:
      行1: 并线要求='单', 线径='0.5', 接口类型='长', 压头名称='0.5单长压头'
      行2: 并线要求='双', 线径='0.3', 接口类型='长', 压头名称='0.3双长压头'
```

## 使用方法

1. 确保输入数据中包含SZ2505308项目标识
2. 运行正常的压头匹配流程
3. 当处理到P01屏柜的0.3线径数据时，会自动显示详细的调试信息
4. 观察调试输出，分析压头匹配的具体过程和结果

## 注意事项

- 调试输出仅在满足特定条件时显示，不会影响其他项目的正常运行
- 调试信息会显示在控制台中，便于实时查看匹配过程
- 如果不需要调试输出，可以在代码中注释掉相关的print语句

## 相关文件

- `src/terminal_matching.py` - 主要的调试代码位置
- `process_device_terminal()` 函数 - 核心调试逻辑所在
